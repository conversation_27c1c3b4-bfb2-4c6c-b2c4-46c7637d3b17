Stack trace:
Frame         Function      Args
0007FFFF9B80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9B80, 0007FFFF8A80) msys-2.0.dll+0x1FE8E
0007FFFF9B80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E58) msys-2.0.dll+0x67F9
0007FFFF9B80  000210046832 (000210286019, 0007FFFF9A38, 0007FFFF9B80, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9B80  000210068E24 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9E60  00021006A225 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCA19A0000 ntdll.dll
7FFCA11B0000 KERNEL32.DLL
7FFC9EAD0000 KERNELBASE.dll
7FFCA1760000 USER32.dll
7FFC9EF90000 win32u.dll
7FFCA1930000 GDI32.dll
7FFC9F110000 gdi32full.dll
7FFC9F5F0000 msvcp_win.dll
7FFC9EFC0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCA06A0000 advapi32.dll
7FFCA0130000 msvcrt.dll
7FFCA05F0000 sechost.dll
7FFCA1290000 RPCRT4.dll
7FFC9DA50000 CRYPTBASE.DLL
7FFC9F250000 bcryptPrimitives.dll
7FFCA1520000 IMM32.DLL
